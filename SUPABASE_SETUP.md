# Supabase Setup Guide for TimeBlock SaaS

This guide will walk you through setting up Supabase for your TimeBlock SaaS application.

## 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/sign in
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: `timeblock-saas`
   - **Database Password**: Generate a strong password (save this!)
   - **Region**: Choose closest to your users
5. Click "Create new project"
6. Wait for the project to be created (2-3 minutes)

## 2. Get Your Project Credentials

1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (something like `https://your-project-id.supabase.co`)
   - **anon public** key
   - **service_role** key (keep this secret!)

## 3. Update Environment Variables

Update your `.env.local` file with the Supabase credentials:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Update DATABASE_URL to point to Supabase
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.your-project-id.supabase.co:5432/postgres"
```

## 4. Set Up Database Schema

### Option A: Using Prisma (Recommended)

1. Update your Prisma schema to point to Supabase:
   ```bash
   npx prisma db push
   ```

2. Generate Prisma client:
   ```bash
   npx prisma generate
   ```

### Option B: Using Supabase SQL Editor

1. Go to **SQL Editor** in your Supabase dashboard
2. Run the SQL commands from `prisma/schema.prisma` to create tables

## 5. Configure Authentication

### Enable Google OAuth in Supabase

1. Go to **Authentication** → **Providers**
2. Enable **Google** provider
3. Add your Google OAuth credentials:
   - **Client ID**: `1009737975616-35d9q9r35e0iqf859419gtqa63ikmm8c.apps.googleusercontent.com`
   - **Client Secret**: `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`
4. Add redirect URLs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://your-domain.com/api/auth/callback/google` (production)

### Configure Site URL

1. Go to **Authentication** → **URL Configuration**
2. Set **Site URL**: `http://localhost:3000` (development)
3. Add **Redirect URLs**:
   - `http://localhost:3000/api/auth/callback/google`
   - `http://localhost:3000/auth/signin`

## 6. Enable Real-time Features

1. Go to **Database** → **Replication**
2. Enable replication for the following tables:
   - `Task`
   - `TimeBlock`
3. This enables real-time subscriptions for live updates

## 7. Set Up Row Level Security (RLS)

Run these SQL commands in the **SQL Editor**:

```sql
-- Enable RLS on all tables
ALTER TABLE "User" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Task" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "TimeBlock" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Account" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Session" ENABLE ROW LEVEL SECURITY;

-- Create policies for Task table
CREATE POLICY "Users can view own tasks" ON "Task"
  FOR SELECT USING (auth.uid()::text = "userId");

CREATE POLICY "Users can insert own tasks" ON "Task"
  FOR INSERT WITH CHECK (auth.uid()::text = "userId");

CREATE POLICY "Users can update own tasks" ON "Task"
  FOR UPDATE USING (auth.uid()::text = "userId");

CREATE POLICY "Users can delete own tasks" ON "Task"
  FOR DELETE USING (auth.uid()::text = "userId");

-- Create policies for TimeBlock table
CREATE POLICY "Users can view own timeblocks" ON "TimeBlock"
  FOR SELECT USING (auth.uid()::text = "userId");

CREATE POLICY "Users can insert own timeblocks" ON "TimeBlock"
  FOR INSERT WITH CHECK (auth.uid()::text = "userId");

CREATE POLICY "Users can update own timeblocks" ON "TimeBlock"
  FOR UPDATE USING (auth.uid()::text = "userId");

CREATE POLICY "Users can delete own timeblocks" ON "TimeBlock"
  FOR DELETE USING (auth.uid()::text = "userId");
```

## 8. Test the Connection

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Check the browser console for any connection errors
3. Try creating a task or time block to test the integration

## 9. Optional: Set Up Database Backups

1. Go to **Settings** → **Database**
2. Enable **Point in Time Recovery** for automatic backups
3. Configure backup retention period

## Troubleshooting

### Common Issues:

1. **Connection errors**: Check your environment variables
2. **Authentication issues**: Verify Google OAuth setup
3. **RLS errors**: Ensure policies are correctly set up
4. **Real-time not working**: Check if replication is enabled

### Useful Commands:

```bash
# Reset Prisma client
npx prisma generate

# Push schema changes
npx prisma db push

# View database in Prisma Studio
npx prisma studio
```

## Next Steps

After setup is complete:

1. Test all CRUD operations
2. Verify real-time subscriptions work
3. Test authentication flow
4. Set up monitoring and alerts
5. Configure production environment

Your Supabase integration is now ready! The application will automatically use Supabase for:
- Database operations
- Real-time updates
- Authentication (alongside NextAuth)
- File storage (if needed in the future)
